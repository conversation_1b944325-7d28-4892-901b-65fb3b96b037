{"root": true, "env": {"browser": true, "es2020": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended"], "ignorePatterns": ["dist", ".eslintrc.json"], "parser": "@typescript-eslint/parser", "plugins": ["react-refresh"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExports": true}], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn"}}