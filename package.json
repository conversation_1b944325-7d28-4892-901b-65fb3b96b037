{"name": "cvic", "private": true, "version": "0.1.0", "homepage": "https://alexandrosliaskos.github.io/CVIc/", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:static": "vite build", "build:fixed": "./build-with-fixes.sh", "build:simple": "./build-simple.sh", "build:production": "./build-production.sh", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "deploy": "npm run build:production && firebase deploy --only hosting", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "serve": "npx http-server ./dist -o -p 8080", "serve:firebase": "firebase serve --only hosting"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@turf/turf": "^6.5.0", "@types/leaflet-draw": "^1.0.11", "@types/shpjs": "^3.4.7", "dom-to-image": "^2.6.0", "firebase": "^10.14.1", "framer-motion": "^12.15.0", "geojson": "^0.5.0", "georaster": "^1.6.0", "georaster-layer-for-leaflet": "^3.10.0", "geotiff": "^2.1.3", "html2canvas": "^1.4.1", "idb": "^8.0.2", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet-image": "^0.4.0", "mermaid": "^11.6.0", "proj4": "^2.9.2", "proj4-fully-loaded": "^0.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.1", "react-router-dom": "^6.30.0", "reactflow": "^11.11.4", "recharts": "^2.15.3", "shpjs": "^6.1.0", "typescript": "^5.3.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/geojson": "^7946.0.14", "@types/jest": "^29.5.12", "@types/leaflet": "^1.9.8", "@types/node": "^20.11.19", "@types/proj4": "^2.5.6", "@types/react": "^18.2.57", "@types/react-dom": "^18.3.5", "@types/unzipper": "^0.10.9", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react-swc": "^3.10.0", "autoprefixer": "^10.4.17", "dotenv": "^16.5.0", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "gh-pages": "^6.1.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.1.3"}}