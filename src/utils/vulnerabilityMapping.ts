// src/utils/vulnerabilityMapping.ts

// ICVI result classification ranges
const ICVI_ARITHMETIC_RANGES = [
  { min: 0, max: 0.2, label: 'Very Low', rank: 1 },
  { min: 0.2, max: 0.4, label: 'Low', rank: 2 },
  { min: 0.4, max: 0.6, label: 'Moderate', rank: 3 },
  { min: 0.6, max: 0.8, label: 'High', rank: 4 },
  { min: 0.8, max: 1.0, label: 'Very High', rank: 5 }
];

const ICVI_GEOMETRIC_RANGES = [
  { min: 0, max: 0.0022, label: 'Very Low', rank: 1 },
  { min: 0.0022, max: 0.0238, label: 'Low', rank: 2 },
  { min: 0.0238, max: 0.0846, label: 'Moderate', rank: 3 },
  { min: 0.0846, max: 0.2042, label: 'High', rank: 4 },
  { min: 0.2042, max: 1.0, label: 'Very High', rank: 5 } // Extended to handle all possible values
];

export const getCviCategory = (score: number | undefined | null, formula?: string): string => {
  if (score === undefined || score === null || isNaN(score)) return 'No Data';

  // For ICVI, the score is now a raw value (0-1) that needs classification
  if (formula === 'icvi-arithmetic') {
    // Use arithmetic mean classification ranges
    const range = ICVI_ARITHMETIC_RANGES.find(r => score >= r.min && (score < r.max || (r.max === 1.0 && score <= r.max)));
    return range ? range.label : 'No Data';
  }

  if (formula === 'icvi-geometric') {
    // Use arithmetic mean classification ranges for geometric mean too
    const range = ICVI_ARITHMETIC_RANGES.find(r => score >= r.min && (score < r.max || (r.max === 1.0 && score <= r.max)));
    return range ? range.label : 'No Data';
  }

  // For traditional CVI (1-5 scale)
  const rank = Math.round(score);
  if (rank <= 1) return 'Very Low';
  if (rank === 2) return 'Low';
  if (rank === 3) return 'Moderate';
  if (rank === 4) return 'High';
  if (rank >= 5) return 'Very High';
  return 'No Data';
};

export const getCviRank = (score: number | undefined | null, formula?: string): number => {
  if (score === undefined || score === null || isNaN(score)) return 0;

  // For ICVI, the score is now a raw value (0-1) that needs classification
  if (formula === 'icvi-arithmetic') {
    // Use arithmetic mean classification ranges
    const range = ICVI_ARITHMETIC_RANGES.find(r => score >= r.min && (score < r.max || (r.max === 1.0 && score <= r.max)));
    return range ? range.rank : 0;
  }

  if (formula === 'icvi-geometric') {
    // Use arithmetic mean classification ranges for geometric mean too
    const range = ICVI_ARITHMETIC_RANGES.find(r => score >= r.min && (score < r.max || (r.max === 1.0 && score <= r.max)));
    return range ? range.rank : 0;
  }

  // For traditional CVI (1-5 scale)
  return Math.round(score);
};
